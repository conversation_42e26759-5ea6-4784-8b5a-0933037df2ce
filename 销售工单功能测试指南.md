# 销售工单功能测试指南

## 功能概述

为销售工单添加了以下特定字段，当用户选择的工单分类中 `saleOrderFlag == 1` 时显示：

## 新增字段列表

### 1. 选择客户（必填）
- **字段名**: `customerId`
- **显示条件**: 销售工单 (`isSaleOrder = true`)
- **功能**: 
  - 支持模糊搜索客户姓名和手机号
  - 下拉选择客户
  - 显示格式：「姓名—手机号」
  - 选择后自动填充 `customerName` 和 `phoneNumber` 字段
- **数据源**: 客户列表接口 (pageSize: 9999, pageNum: 1)

### 2. 是否需要面访（非必填）
- **字段名**: `needVisit`
- **显示条件**: 销售工单
- **默认值**: 不需要 ('0')
- **选项**: 不需要('0') / 需要('1')
- **逻辑**: 选择「不需要」时隐藏面访时间；选择「需要」时显示面访时间且必填

### 3. 面访时间（条件必填）
- **字段名**: `visitTime`
- **显示条件**: 销售工单 && 需要面访
- **格式**: YYYY-MM-DD HH:mm
- **必填条件**: 当选择「需要面访」时

### 4. 预约外呼时间（非必填）
- **字段名**: `callTime`
- **显示条件**: 销售工单
- **格式**: YYYY-MM-DD HH:mm

### 5. 问题描述（非必填）
- **字段名**: `problemDescription`
- **显示条件**: 销售工单
- **功能**: 复用现有的「工单内容」功能

### 6. 流转方式调整
- **运维受理人字段**: 销售工单时隐藏
- **工单受理人字段**: 销售工单时正常显示

## 测试步骤

### 1. 基础功能测试
1. 打开工单创建页面
2. 选择一个 `saleOrderFlag = 1` 的工单分类
3. 验证销售工单特定字段是否显示
4. 选择一个 `saleOrderFlag = 0` 的工单分类
5. 验证销售工单特定字段是否隐藏

### 2. 客户选择功能测试
1. 点击「选择客户」下拉框
2. 验证是否加载客户列表
3. 输入关键字进行模糊搜索
4. 选择一个客户
5. 验证客户姓名和手机号是否自动填充

### 3. 面访时间逻辑测试
1. 选择「不需要面访」
2. 验证面访时间字段是否隐藏
3. 选择「需要面访」
4. 验证面访时间字段是否显示且必填
5. 不填写面访时间提交，验证是否有必填校验

### 4. 时间格式测试
1. 选择面访时间，验证格式是否为 YYYY-MM-DD HH:mm
2. 选择预约外呼时间，验证格式是否为 YYYY-MM-DD HH:mm

### 5. 数据提交测试
1. 填写完整的销售工单信息
2. 提交工单
3. 验证后端是否正确接收所有销售工单字段
4. 检查时间字段格式是否正确转换

### 6. 字段重置测试
1. 填写销售工单信息
2. 取消弹窗
3. 重新打开弹窗
4. 验证销售工单字段是否正确重置

## 注意事项

1. 确保工单分类数据中包含 `saleOrderFlag` 字段
2. 客户列表接口需要正常返回数据
3. 时间字段提交时会自动转换为 `YYYY-MM-DD HH:mm:ss` 格式
4. 销售工单时运维受理人字段会被隐藏
5. 问题描述字段复用工单内容的富文本编辑器

## API 接口

### 客户列表接口
- **URL**: `/customer-ticket/customer/list`
- **方法**: POST
- **参数**: 
  ```json
  {
    "pageSize": 9999,
    "pageNum": 1,
    "customerName": "搜索关键字",
    "phoneNumber": "搜索关键字"
  }
  ```

### 工单创建接口
- **URL**: `/customer-ticket/workOrder/add`
- **新增字段**:
  - `customerId`: 客户ID
  - `customerName`: 客户姓名
  - `needVisit`: 是否需要面访
  - `visitTime`: 面访时间
  - `callTime`: 预约外呼时间
  - `problemDescription`: 问题描述
