import request from '@/utils/system/request';

// 客户列表查询
export function customerListAPI(params) {
  console.log('客户列表接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customerInfo/queryList',
    method: 'post',
    data: params,
  });
}

// 客户新增
export function customerAddAPI(params) {
  console.log('客户新增接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customerInfo/save',
    method: 'post',
    data: params,
  });
}

// 客户详情
export function customerDetailAPI(params) {
  console.log('客户详情接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customerInfo/customerDetailInfo',
    method: 'post',
    data: params,
  });
}

// 客户更新
export function customerUpdateAPI(params) {
  console.log('客户更新接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customerInfo/save',
    method: 'post',
    data: params,
  });
}

// 客户删除
export function customerDeleteAPI(params) {
  console.log('客户删除接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customer/delete',
    method: 'post',
    data: params,
  });
}

// 客户导出
export function customerExportAPI(params) {
  console.log('客户导出接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customerInfo/export',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}

// 检查手机号是否已存在
export function checkPhoneExistAPI(params) {
  console.log('检查手机号是否存在接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customer/checkPhone',
    method: 'post',
    data: params,
  });
}

// 获取客户类型选项（支持自动完成）
export function getCustomerTypeOptionsAPI(params) {
  console.log('获取客户类型选项接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customer/customerTypeOptions',
    method: 'post',
    data: params,
  });
}

// 获取公司行业选项（支持自动完成）
export function getCompanyIndustryOptionsAPI(params) {
  console.log('获取公司行业选项接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customer/companyIndustryOptions',
    method: 'post',
    data: params,
  });
}

// 获取意向程度选项（支持自动完成）
export function getIntentionLevelOptionsAPI(params) {
  console.log('获取意向程度选项接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customer/intentionLevelOptions',
    method: 'post',
    data: params,
  });
}

// 获取客户等级选项（支持自动完成）
export function getCustomerLevelOptionsAPI(params) {
  console.log('获取客户等级选项接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customer/customerLevelOptions',
    method: 'post',
    data: params,
  });
}

// 获取公司名称选项（支持自动完成）
export function getCompanyNameOptionsAPI(params) {
  console.log('获取公司名称选项接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customer/companyNameOptions',
    method: 'post',
    data: params,
  });
}

// 获取跟进人选项（支持自动完成）
export function getFollowUserOptionsAPI(params) {
  console.log('获取跟进人选项接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customer/followUserOptions',
    method: 'post',
    data: params,
  });
}

// 获取岗位名称选项（支持自动完成）
export function getPositionOptionsAPI(params) {
  console.log('获取岗位名称选项接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customer/positionOptions',
    method: 'post',
    data: params,
  });
}

// 获取客户标签选项（支持自动完成）
export function getCustomerTagOptionsAPI(params) {
  console.log('获取客户标签选项接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customer/customerTagOptions',
    method: 'post',
    data: params,
  });
}

// 统一的字段选项数据获取接口
export function getFieldOptionsAPI(params) {
  console.log('获取字段选项数据接口', params);
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customerInfo/queryDistinctValue',
    method: 'post',
    data: params,
  });
}

// 批量导入客户信息
export function customerBatchImportAPI(formData) {
  console.log('批量导入客户信息接口');
  return request({
    url: process.env.VUE_APP_SYSTEM_API + '/customer-ticket/customerInfo/importCustomerInfo',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
